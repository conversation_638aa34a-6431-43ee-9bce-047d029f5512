import * as os from 'os'; // 导入 os 模块
import path from 'path';
import fs from 'fs';

import { defineConfig } from '@rsbuild/core';
import { pluginBabel } from '@rsbuild/plugin-babel';
import { pluginLess } from '@rsbuild/plugin-less';
import { pluginVue } from '@rsbuild/plugin-vue';
import { pluginVueJsx } from '@rsbuild/plugin-vue-jsx';

import antdStyle from '@paas/paas-library/src/theme/antd-sty.json';

import getAppConfig from './app-env/config.js';
import { formatOldAdmin } from './plugins/format-old-admin.js';

const compilationPackLList = () => {
  const list = ['admin-library', '@paas/paas-library'];

  return list.map(item => {
    return new RegExp(`/node_modules[\\/]${item}[\\/]/`);
  });
};

// Framework 配置文件路径
const FRAMEWORK_CONFIG_URL = 'public/framework.config.json';

// 解析路径的辅助函数
const resolve = (dir: string) => path.join(process.cwd(), dir);

const isProd = JSON.parse(process.env.isProd as string);
const isDev = JSON.parse(process.env.isDev as string);
const isLocal = JSON.parse(process.env.isLocal as string);
const isTest = JSON.parse(process.env.isTest as string);

const port = 7073; // 定义端口号，与 server.port 一致

console.log('isProd, isDev, isLocal, isTest', isProd, isDev, isLocal, isTest);

const appConfig = getAppConfig(isProd, isDev, isLocal, isTest);

// 使用原生 Node.js os 模块获取 IP 地址的辅助函数
const getNativeIpAddress = () => {
  const networkInterfaces = os.networkInterfaces();
  let ipAddress = '127.0.0.1'; // 默认 IP 地址

  const yiT = networkInterfaces['以太网'];

  if (yiT) {
    for (const iface of yiT) {
      // 跳过内部地址 (如 127.0.0.1), 非 IPv4 地址, 以及链路本地地址 (169.254.x.x)
      if (iface.family === 'IPv4' && !iface.internal) {
        ipAddress = iface.address;
        return ipAddress; // 返回找到的第一个合适的 IP 地址
      }
    }
  }

  return ipAddress; // 如果没有找到其他合适的 IP，则返回默认值或最后一个检查到的值
};

const getMCDevUrl = () => {
  if (!isLocal) {
    return undefined;
  }

  const { enName } = appConfig;

  const rawIp = getNativeIpAddress(); // 使用原生函数获取 IP
  const ip = rawIp.replace(/\./g, '-'); // 应用原有的 IP 格式化

  // 使用之前定义的 port 常量
  return `https://a.mucang.cn/?project=${enName}&_devApp=https%3A%2F%2F${ip}-${port}.local.mucang.cn%2F${enName}%2Fframework.config.json`;
};

formatOldAdmin(isProd, isLocal);

/**
 * 更新 framework.config.json 配置文件
 * 根据 app-env 配置修改 framework.config.json 中的 appList 和 hosts
 */
const updateFrameworkConfig = () => {
  const realUrl = resolve(FRAMEWORK_CONFIG_URL);
  const frameworkStr = fs.readFileSync(realUrl, 'utf-8');

  if (!frameworkStr || frameworkStr.trim() === '') {
    console.warn('Framework config file is empty, skipping update');
    return;
  }

  const framework = JSON.parse(frameworkStr);
  let newFrameworkStr = '';
  const hosts: Record<string, string> = {};

  Object.keys(appConfig.domain).forEach(key => {
    // base 那边需要的域名是带 / 结尾的
    hosts[key] = appConfig.domain[key] + '/';
  });

  framework.appList = appConfig.appList;
  framework.initialize.hosts = hosts;

  newFrameworkStr =
    JSON.stringify(framework, null, '\t').replace(new RegExp(/\[[^,]*\]/g), str => str.replace(/[\s\t]/g, '')) + '\n';

  fs.writeFile(realUrl, newFrameworkStr, 'utf-8', () => {
    console.log('config 环境相关配置写入成功');
  });
};

// 执行配置更新
updateFrameworkConfig();

export default defineConfig({
  plugins: [
    pluginBabel({
      include: /\.(?:jsx|tsx)$/
    }),
    pluginVue(),
    pluginVueJsx(),
    pluginLess({
      lessLoaderOptions: {
        lessOptions: {
          modifyVars: antdStyle
        }
      }
    })
  ],
  source: {
    entry: {
      index: {
        import: './src/main.ts'
      }
    },
    include: compilationPackLList(),
    define: {
      APP: JSON.stringify(appConfig)
    }
  },
  performance: {
    removeConsole: isProd ? ['log'] : false
  },
  server: {
    port: port, // 使用定义的 port 常量
    open: getMCDevUrl(),
    base: '/mclaren'
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      library: path.resolve(__dirname, 'node_modules/paas-library')
    }
  },
  output: {
    externals: {
      fabric: 'fabric'
    },
    sourceMap: isLocal || isDev,
    legalComments: 'none',
    minify: isProd,
    assetPrefix: isLocal ? '/mclaren' : isTest ? '/mclaren-ttt' : '/mclaren'
  },
  html: {
    title: '驾考宝典企业版·木仓科技荣誉出品·mclaren-jiaxiao',
    template: './public/index.html'
  }
});

import path from 'path';
import fs from 'fs';
import isEqual from 'lodash/isEqual';
import { getAppUrlMap } from './app-url';

const FRAMEWORK_CONFIG_URL = 'public/framework.config.json';

const resolve = dir => path.join(process.cwd(), dir); // 路径

const realUrl = resolve(FRAMEWORK_CONFIG_URL);

let appUrlMap: Record<string, URL> = null;

const useSetUrl = () => {
  const appNameMatchRegexp = /aside=(.*?)!app/;

  return function (node) {
    try {
      const matchRes = node.href.match(appNameMatchRegexp);

      const appName = matchRes ? matchRes[1] : null;

      const appBaseUrl = appName ? appUrlMap[appName] : null;

      if (!appBaseUrl) {
        return;
      }

      const nodeUrl = new URL(node.href);

      appBaseUrl.hash = nodeUrl.hash;

      node.href = appBaseUrl.href;
    } catch (error) {}
  };
};

const deepUpdate = (nodes, setUrl) => {
  if (!nodes) {
    return;
  }

  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];

    if (node.nodes) {
      deepUpdate(node.nodes, setUrl);
    }

    if (node.href) {
      setUrl(node);
    }
  }
};

const createUrl = appName => {
  const baseUrl = appUrlMap[appName].href;

  return `${baseUrl}#header=${appName}!main%2FnewAdmin&aside=${appName}!app%2Fnew-admin-placeholder%2Findex%2Fmain`;
};

const setHiddenPlaceholderMenu = menu => {
  const appList = Object.keys(appUrlMap);

  const placeholderNameList = Object.keys(appUrlMap).map(key => {
    return 'placeholder-' + key;
  });

  // 删除老的
  for (let i = 0; i < menu.length; i++) {
    const item = menu[i];

    if (placeholderNameList.includes(item.id)) {
      menu.splice(i, 1);
      i--;
    }
  }

  // 添加新的
  for (let i = 0; i < appList.length; i++) {
    const appName = appList[i];

    if (appUrlMap[appName]) {
      menu.push({
        title: appName,
        id: 'placeholder-' + appName,
        href: createUrl(appName)
      });
    }
  }
};

const formatOldAdmin = (isProd, isLocal) => {
  let frameworkStr = fs.readFileSync(realUrl, 'utf-8');

  if (!frameworkStr || frameworkStr.trim() === '') {
    console.warn('Framework config file is empty, skipping formatOldAdmin');
    return;
  }

  const frameworkJson = JSON.parse(frameworkStr);

  appUrlMap = getAppUrlMap(isProd, isLocal);

  const setUrl = useSetUrl();

  setHiddenPlaceholderMenu(frameworkJson.hidden.menu);

  deepUpdate(frameworkJson.side.menu, setUrl);

  if (!isEqual(frameworkJson, JSON.parse(frameworkStr))) {
    const newStr = JSON.stringify(frameworkJson, null, '\t');

    frameworkStr = newStr.replace(new RegExp(/\[[^,]*\]/g), str => str.replace(/[\s\t]/g, '')) + '\n';

    fs.writeFile(realUrl, frameworkStr, 'utf-8', () => {
      console.log('config 环境相关配置写入成功');
    });
  }
};

export { formatOldAdmin };
